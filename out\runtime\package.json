{"name": "classic-level", "version": "3.0.0", "description": "An abstract-level database backed by LevelDB", "license": "MIT", "main": "index.js", "types": "./index.d.ts", "scripts": {"install": "node-gyp-build", "test": "standard && (nyc -s tape test/*-test.js | tap-arc) && nyc report", "test-pessimistic": "tape test/*-test.js | tap-arc -pv", "test-gc": "node --expose-gc test/gc.js", "test-electron": "electron test/electron.js", "test-prebuild": "cross-env PREBUILDS_ONLY=1 npm t", "coverage": "nyc report -r lcovonly", "rebuild": "npm run install --build-from-source", "prebuild": "prebuildify -t 18.20.4 --napi --strip", "download-prebuilds": "prebuildify-ci download", "hallmark": "hallmark --fix", "dependency-check": "dependency-check --no-dev -i napi-macros . test/*.js", "prepublishOnly": "npm run dependency-check", "prebuild-linux-arm": "prebuildify-cross -i linux-armv6 -i linux-armv7 -i linux-arm64-lts -t 18.20.4 --napi --strip", "prebuild-android-arm": "prebuildify-cross -i android-armv7 -i android-arm64 -t 18.20.4 --napi --strip", "prebuild-linux-x64": "prebuildify-cross -i almalinux-devtoolset11 -i alpine -t 18.20.4 --napi --strip", "prebuild-darwin-x64+arm64": "prebuildify -t 18.20.4 --napi --strip --arch x64+arm64", "prebuild-win32-x86": "prebuildify -t 18.20.4 --napi --strip", "prebuild-win32-x64": "prebuildify -t 18.20.4 --napi --strip"}, "dependencies": {"abstract-level": "^3.1.0", "module-error": "^1.0.1", "napi-macros": "^2.2.2", "node-gyp-build": "^4.3.0"}, "devDependencies": {"@types/node": "^22.14.1", "@voxpelli/tsconfig": "^15.1.2", "cross-env": "^7.0.3", "dependency-check": "^4.1.0", "du": "^1.0.0", "electron": "^35.2.0", "glob": "^10.4.5", "hallmark": "^5.0.1", "mkfiletree": "^2.0.0", "node-gyp": "^11.2.0", "nyc": "^17.1.0", "prebuildify": "^6.0.1", "prebuildify-ci": "^1.0.4", "prebuildify-cross": "^5.1.1", "readfiletree": "^1.0.0", "standard": "^17.0.0", "tap-arc": "^1.3.2", "tape": "^5.9.0", "tempy": "^1.0.1", "typescript": "^5.8.3"}, "gypfile": true, "repository": {"type": "git", "url": "https://github.com/Level/classic-level.git"}, "homepage": "https://github.com/Level/classic-level", "keywords": ["leveldb", "level"], "engines": {"node": ">=18"}}